"use client";

import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import { useRouter } from "next/navigation";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: "USER" | "ADMIN" | "HR";
}

export function ProtectedRoute({ children, requiredRole = "USER" }: ProtectedRouteProps) {
  const [loading, setLoading] = useState(true);
  const [authorized, setAuthorized] = useState(false);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        console.log("No session found, redirecting to home");
        router.push("/");
        return;
      }

      // Check user role in database
      const response = await fetch('/api/user/me', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        console.log("Failed to fetch user data, redirecting to home");
        router.push("/");
        return;
      }

      const userData = await response.json();
      
      if (!userData.user || userData.user.role !== requiredRole) {
        console.log("User role mismatch, redirecting to unauthorized");
        router.push("/unauthorized");
        return;
      }

      setAuthorized(true);
    } catch (error) {
      console.error("Auth check failed:", error);
      router.push("/");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (!authorized) {
    return null; // Will redirect
  }

  return <>{children}</>;
}
