// app/api/auth/signup/route.ts (Next.js App Router)
import { supabase } from "@/lib/supabase";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

const signupSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export async function POST(req: Request) {
  const body = await req.json();
  const parsed = signupSchema.safeParse(body);

  if (!parsed.success) {
    return new Response(JSON.stringify({ error: parsed.error.format() }), { status: 400 });
  }

  const { email, password } = parsed.data;

  // Supabase signup
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });

  if (error) return new Response(JSON.stringify({ error: error.message }), { status: 400 });

  // Sync with Prisma DB
  await prisma.user.create({
    data: {
      supabaseId: data.user?.id!,
      email,
    },
  });

  return new Response(JSON.stringify({ user: data.user }));
}
