import { logout } from "@/components/logout";
import { supabase } from "@/lib/supabase";
import { prisma } from "@/lib/prisma";
import { redirect } from "next/navigation";

export default async function AdminPage() {

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/");
  }

  // Check role in Prisma
  const dbUser = await prisma.user.findUnique({
    where: { supabaseId: user.id },
  });

  if (dbUser?.role !== "ADMIN") {
    redirect("/unauthorized");
  }

  return (
    <>
      <h1 className="text-2xl font-bold">Welcome Admin 🚀</h1>
      <p className="mt-4">This is the admin page.</p>
      <button onClick={logout}>Logout</button>
    </>
  );
}
