// app/user/layout.tsx
import { prisma } from "@/lib/prisma";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function UserLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = createServerComponentClient({ cookies });
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session?.user) redirect("/login");

  const user = await prisma.user.findUnique({
    where: { supabaseId: session.user.id },
  });

  if (!user || user.role !== "USER") redirect("/unauthorized");

  return <>{children}</>;
}
