// app/user/layout.tsx
import { prisma } from "@/lib/prisma";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function UserLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookieStore.set(name, value, options);
          });
        },
      },
    }
  );
  const {
    data: { session },
  } = await supabase.auth.getSession();

  console.log("User layout - session:", session?.user?.id || "No session");

  if (!session?.user) {
    console.log("User layout - No session, redirecting to /");
    redirect("/");
  }

  const user = await prisma.user.findUnique({
    where: { supabaseId: session.user.id },
  });

  console.log("User layout - DB user:", user?.id || "No user found", "Role:", user?.role);

  if (!user || user.role !== "USER") {
    console.log("User layout - Invalid user or role, redirecting to /unauthorized");
    redirect("/unauthorized");
  }

  return <>{children}</>;
}
